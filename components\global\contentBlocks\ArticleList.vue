<script setup lang="ts">
import { useI18n } from "vue-i18n";

const target = "articles";
const { locale, defaultLocale } = useI18n();
const route = useRoute();

const cleanedPath = getPathWithoutLocale(
	route.path,
	locale.value,
	defaultLocale,
);

const props = defineProps({
	content: {
		type: String,
		required: true,
	},
	meta: {
		type: Object,
		required: false,
		default: () => ({}),
	},
});

if (!props.meta.id) {
	throw new Error("meta.id is required for data fetching");
}

const { data } = useAsyncData<Array<WebArticleEntity>>("articlesData", () =>
	$fetch<Array<WebArticleEntity>>(`/api/${target}`, {
		params: {
			locale: locale.value,
			slug: cleanedPath,
			id: props.meta.id,
		},
	}),
);

const sortedArticles = computed(() => {
	if (!data.value) return [];
	return data.value.sort((a, b) => {
		const dateA = new Date(a.attributes?.publishDate ?? 0);
		const dateB = new Date(b.attributes?.publishDate ?? 0);
		return dateB.getTime() - dateA.getTime();
	});
});
</script>

<template>
  <div v-html="props.content" v-auto-id></div>

  <div class="content" v-if="data && data.length">
    <div class="content-container">
      <ArticleListItem
        v-for="article in sortedArticles"
        :key="article.id?.toString()"
        :article="article"
      />
    </div>
  </div>
</template>
