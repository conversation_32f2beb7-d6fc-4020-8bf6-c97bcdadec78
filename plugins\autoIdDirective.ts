// plugins/autoIdDirective.ts

// Automatically add an ID to all headings within an element based on the text content, so that they can be linked to directly
export default defineNuxtPlugin((nuxtApp) => {
	nuxtApp.vueApp.directive("auto-id", {
		mounted(el: HTMLElement) {
			// Recursive function to process all heading tags within the element
			const addIdToHeadings = (element: HTMLElement) => {
				const headings = element.querySelectorAll("h1, h2, h3, h4, h5, h6");
				for (const heading of headings) {
					// Check if the heading doesn't already have an ID
					if (!heading.id) {
						// Generate an ID based on the text content
						heading.id = heading.textContent
							? heading.textContent
									.toLowerCase()
									.normalize("NFKD") // Normalize diacritics fully
									.replace(/\p{M}/gu, "") // Remove combining marks
									.replace(/\s+/g, "-") // Replace spaces with dashes
									.replace(/[^a-z0-9-]/g, "") // Keep only alphanumerics and dashes
							: "";
					}
				}
			};

			// Apply the function to the initial element
			addIdToHeadings(el);
		},
	});
});
