<script setup lang="ts">
import logo from "@/public/logo.webp";
import smallLogo from "@/public/logo_short.webp";
import { useFishBushStore } from "@/stores/FishBush";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

const FBstore = useFishBushStore();
const resource = "web-menu-items";
const { locale } = useI18n();
const event = useRequestEvent();

const modifiers: FBQueryParams = {
	locale: locale.value,
};
const resourceKey = `${resource}-${locale.value}`;

const { data: webMenuData } = await useAsyncData(resourceKey, async () => {
	if (!FBstore.getFBdata(resource, modifiers)) {
		await FBstore.fetchFishBushData(resource, modifiers);
	}
	return FBstore.getFBdata(resource, modifiers);
});

const webMenuItems = computed(() =>
	(webMenuData.value ?? [])
		.filter((item): item is WebMenuItem => item != null)
		.filter(
			(item) =>
				item.menuType === Enum_Webmenuitem_Menutype.TopNav && item.isValid,
		)
		.sort((a, b) => (a.weight ?? 0) - (b.weight ?? 0)),
);
const isMenuOpen = ref(false);

function toggleMenu() {
	// toggle aria-expanded attribute
	isMenuOpen.value = !isMenuOpen.value;
}

function getHomeLink() {
	return locale.value === event?.context?.config?.defaultLocale
		? "/"
		: `/${locale.value}`;
}
</script>

<template>
  <!-- Navbar -->
  <nav
    class="print:absolute fixed top-0 left-0 right-0 z-30 flex flex-wrap items-center justify-between py-2 shadow-md bg-gradient-to-r from-tda-200 to-tda-300 lg:flex-row lg:flex-nowrap lg:justify-start">
    <div class="content-container flex flex-wrap items-center justify-between mx-auto px-4">
      <a class="text-white inline-block py-[0.3125rem] mr-4 whitespace-nowrap" :href="getHomeLink()">
        <img :src="logo" alt="Tour de App" class="max-sm:max-h-[38px] max-h-11 hidden xs:block">
        <img :src="smallLogo" alt="Tour de App" class="max-h-[38px] block xs:hidden">
      </a>
      <button class="pt-1 pr-0 pb-1 pl-3 text-lg/4 bg-transparent border-transparent rounded outline-0 lg:hidden"
        :disabled="false" aria-label="Toggle navigation" :aria-expanded="isMenuOpen" @click="toggleMenu">
        <span class="w-8 h-8 bg-center bg-no-repeat bg-contain inline-block align-middle"
          :style="{ backgroundImage: `url(/hamburger-menu.svg)` }"></span>
      </button>
      <!-- Collapsible content -->
      <div ref="menuRef"
        class="flex-grow items-center lg:basis-auto lg:flex basis-full overflow-hidden transition-all duration-300 ease-[ease] max-h-0 lg:max-h-max aria-expanded:max-h-80"
        :aria-expanded="isMenuOpen"
        >
        <!-- Links -->
        <ul v-if="webMenuItems" class="ml-auto flex flex-col lg:flex-row pl-0 mb-0 list-none mt-0">
          <li v-for="menuItem in webMenuItems" :key="menuItem.documentId" class="nav-item">
            <a :href="menuItem.link" class="nav-link">{{ menuItem.text }}</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.nav-item {
  @apply pl-4 text-xl lg:px-3;
}

.nav-link {
  @apply text-white transition-all duration-300 ease-[ease] hover:text-opacity-75 max-lg:px-[6px] block py-5;
}
</style>
