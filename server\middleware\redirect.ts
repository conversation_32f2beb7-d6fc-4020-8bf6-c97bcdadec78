/**
 * /server/middleware/redirect.ts
 * This middleware handles CMS based redirects (307, 308) and possibly throws 500 - all server side.
 * The 4xx errors are handled in the [...slug].vue page component (e.g. no blocks produce 404)
 */

import type { WebConfigResponse } from "@/models/FishBushCustomTypes";
import { Enum_Webpage_Redirecttype } from "@/models/types";
import { processHtmlContent } from "@/utils/dataUtils";
import { getPathWithoutLocale } from "@/utils/localeUtils";
import { getRequestURL, sendRedirect } from "h3";
import { StatusCodes as SC } from "http-status-codes";
import { FetchError } from "ofetch";

export default defineEventHandler(async (event) => {
	const url = getRequestURL(event);
	const urlPath = url.pathname;
	// Skip processing for API and static files
	if (
		urlPath.startsWith("/api") ||
		urlPath.startsWith("/_nuxt") ||
		urlPath.startsWith("/__nuxt") ||
		urlPath.startsWith("/uploads")
	) {
		return;
	}

	// Find what is my public URL
	console.info(`[Server MW] Public URL: ${url.origin}`);

	// Extract locale from URL path if present
	// Match patterns like /en/... or /cs/...
	const localeRegex = /^\/([a-z]{2})(\/|$)/;
	const localeMatch = urlPath.match(localeRegex);
	const pathLocale = localeMatch ? localeMatch[1] : null;

	// Always try to call /api/config (has a cache)
	let configResponse: WebConfigResponse;
	try {
		const params: Record<string, string> = { origin: url.origin };
		if (pathLocale) {
			params.locale = pathLocale;
		}

		const response = await $fetch<WebConfigResponse>("/api/config", { params });
		configResponse = response;

		// Store the config response in event context
		event.context.config = configResponse;
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);
		// If we get no response, just return
		return;
	}

	// if the path locale is the same as the default locale, remove it
	if (pathLocale === configResponse.defaultLocale) {
		return sendRedirect(event, urlPath.replace(localeRegex, "/"), SC.PERMANENT_REDIRECT);
	}

	// Get default locale from API config response instead of runtime config
	const defaultLocale = configResponse?.defaultLocale || "cs"; // fallback to 'cs' if not available
	const locale = pathLocale ? pathLocale : defaultLocale;



	// Get path without locale prefix
	const cleanedPath = getPathWithoutLocale(urlPath, locale, defaultLocale);

	const modifiers: FBQueryParams = {
		slug: cleanedPath,
		locale: locale,
	};

	// Call /api/web (has a cache) instead of direct external API call
	try {
		const res = await $fetch<WebPage>("/api/web", { params: modifiers });

		// No blocks indicates either redirect or 404
		if (!res.blocks) {
			return;
		}

		if (res.blocks.length === 0) {
			if (res.noContentRedirectUrl) {
				return sendRedirect(
					event,
					res.noContentRedirectUrl,
					res.redirectType === Enum_Webpage_Redirecttype.Permanent
						? SC.PERMANENT_REDIRECT
						: SC.TEMPORARY_REDIRECT,
				);
			}

			return;
		}

		// biome-ignore lint/suspicious/noExplicitAny: No Error is possible on this level
		for (const block of res.blocks as any as ContentBlock[]) {
			if (!block.content) continue;
			block.content = processHtmlContent(block.content, locale);
		}

		/**
		 * To avoid refetching in the page component, we store the data in the event context
		 * This is then read in [...slug].vue and prevents the need for useAsyncData
		 */
		event.context.webPageData = res;
		event.context.locale = res.locale;
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);

		if (error instanceof FetchError) {
			// It's a FetchError
			event.context.error = createError({
				statusCode: error.statusCode,
				statusMessage: error.statusMessage,
			});
		} else {
			// It's another type of error
			event.context.error = createError({
				statusCode: SC.INTERNAL_SERVER_ERROR,
				statusMessage: String(error),
			});
		}

		// Don't throw the error, just return
		return;
	}
});
