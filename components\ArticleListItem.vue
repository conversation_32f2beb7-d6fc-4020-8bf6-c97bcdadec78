<script setup lang="ts">
import { createImageUrls } from "@/utils/dataUtils.js";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const { locale } = useI18n();
const router = useRouter();

defineProps({
	article: {
		type: Object as PropType<WebArticleEntity>,
		required: true,
	},
});

function formatDate(dateString: string): string {
	const date = new Date(dateString);
	return date.toLocaleDateString(locale.value);
}

function getAuthorName(authorData: UsersPermissionsUserEntity | null): string {
	if (!authorData || !authorData.attributes) return "";
	const { firstname, lastname } = authorData.attributes;
	return `${firstname} ${lastname}`;
}

function openArticle(slug: string): void {
	const path = router.currentRoute.value.path + slug;
	router.push(path);
}

function onAnotationClick(event: MouseEvent): void {
	const target = event.target as HTMLElement;
	if (target.closest("a")) {
		// Clicked on a link inside the anotation
		event.stopPropagation(); // Prevent the click from bubbling up
	}
}
</script>

<template>
  <div
    @click="openArticle(article.attributes?.slug ?? '/')"
    @keypress.enter="openArticle(article.attributes?.slug ?? '/')"
    @keypress.space.prevent="openArticle(article.attributes?.slug ?? '/')"
    tabindex="0"
    role="link"
    class="cursor-pointer card tda-shadow flex flex-col lg:flex-row mb-4"
  >

      <!-- Image Container -->
      <div class="lg:w-[400px] flex-shrink-0">
        <picture>
          <!-- Serve medium image on md and smaller screens -->
          <source
            :srcset="createImageUrls(article.attributes?.coverImage?.data as UploadFileEntity).mediumImageUrl"
            media="(max-width: 768px)"
          />
          <!-- Serve large image on larger screens -->
          <img
            :src="createImageUrls(article.attributes?.coverImage?.data as UploadFileEntity).largeImageUrl"
            :alt="article.attributes?.coverImage?.data?.attributes?.alternativeText || article.attributes?.title"
            class="w-full h-auto lg:h-full object-cover"
          />
        </picture>
      </div>

      <!-- Content Container -->
      <div class="p-4 lg:pl-8 flex flex-col justify-between">
        <div>
          <h2 class="text-3xl text-informative mb-2">
            {{ article.attributes?.title }}
          </h2>
          <div class="text-sm text-gray-500 mb-4">
            {{ formatDate(article.attributes?.publishDate) }}
            <span v-if="article.attributes?.showAuthor">
              | {{ getAuthorName(article.attributes?.author?.data as UsersPermissionsUserEntity) }}
            </span>
          </div>
          <!-- Anotation Container with Click Handler -->
          <div @click="onAnotationClick">
            <div v-html="article.attributes?.anotation" v-auto-id></div>
          </div>
        </div>
      </div>
  </div>
</template>
