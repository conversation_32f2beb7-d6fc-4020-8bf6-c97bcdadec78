/* /server/api/[...slug].ts */

import { getBaseUrl } from "@/utils/dataUtils";
import { type H3Event, createError, getQuery, sendError } from "h3";

const { FISHBUSH_API_KEY, DEFAULT_LOCALE } = useRuntimeConfig();

export default defineEventHandler(async (event: H3Event) => {
	function customError(code = 404, message = "Page not found") {
		return sendError(
			event,
			createError({ statusCode: code, statusMessage: message }),
		);
	}

	// Check if event.path is a valid string
	const path = typeof event.path === "string" ? event.path : "/";

	// Ensure base URL ends with a slash
	const baseUrl = getBaseUrl();

	// Remove leading slash from the path to avoid overriding the base URL path
	const sanitizedPath = path.startsWith("/") ? path.substring(1) : path;
	const fullUrl: URL = new URL(sanitizedPath, baseUrl);

	// Get query parameters from the request
	const query = getQuery(event);

	// Pass all query parameters to the external API
	for (const [key, value] of Object.entries(query)) {
		if (Array.isArray(value)) {
			for (const v of value) {
				fullUrl.searchParams.append(key, v);
			}
		} else {
			fullUrl.searchParams.set(key, value as string);
		}
	}

	// Set default locale if not provided
	if (!fullUrl.searchParams.has("locale")) {
		fullUrl.searchParams.set("locale", DEFAULT_LOCALE);
	}

	// Get the locale
	const locale = fullUrl.searchParams.get("locale") || DEFAULT_LOCALE;

	try {
		const response = await fetch(fullUrl.href, {
			headers: {
				Authorization: `Bearer ${FISHBUSH_API_KEY}`,
			},
		});

		if (!response.ok) {
			return customError(response.status);
		}

		const json = await response.json();

		// New logic for /web-menu-items
		if (fullUrl.pathname.endsWith("/web-menu-items")) {
			return processWebMenuItems(json.data ?? [], locale);
		}

		// Return the unmodified response for other endpoints
		return json;
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);
		return customError(500, `${error}`);
	}
});

// Helper function to process /web-menu-items data
// biome-ignore lint/suspicious/noExplicitAny: Unsafe JSON parsing
function processWebMenuItems(items: any[], locale: string) {
	for (const item of items) {
		if (typeof item.link !== "string") continue;

		if (
			/^[a-zA-Z][a-zA-Z\d+\-.]*:/.test(item.link) ||
			item.link.startsWith("//")
		)
			continue;

		if (locale !== DEFAULT_LOCALE) {
			const localePrefix = `/${locale}`;
			if (
				!item.link.startsWith(`${localePrefix}/`) &&
				item.link !== localePrefix
			) {
				const newLink = `${localePrefix}${item.link.startsWith("/") ? "" : "/"}${item.link}`;
				item.link = newLink;
			}
		}
	}
	return items;
}
