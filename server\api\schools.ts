import { getBaseUrl } from "@/utils/dataUtils";
// /server/api/contacts.ts
import { createError, defineEvent<PERSON>and<PERSON> } from "h3";
import { parse, stringify } from "qs";
const { FISHBUSH_API_KEY } = useRuntimeConfig();

let cachedSchools: Array<SchoolEntity> = [];
let cacheTimestamp: Date = new Date(0);

export default defineEventHandler(async (event) => {
	const currentTime = new Date();
	// Clear cache every 5 minutes
	const flushFrequency = 5 * 60 * 1000;
	if (
		cachedSchools.length > 0 &&
		currentTime.getTime() - cacheTimestamp.getTime() < flushFrequency
	) {
		return cachedSchools;
	}
	cacheTimestamp = currentTime;

	const baseUrl = getBaseUrl();

	// Fetch top region with all its children and join with schools
	const fullUrlRegions = new URL("api/regions/1", baseUrl);

	// get the q parameter from the request
	const schoolTypeFilter = parse(getQuery(event)).q;
	const schoolTypes = Array.isArray(schoolTypeFilter)
		? schoolTypeFilter
		: [schoolTypeFilter];

	const schoolFields = [
		"name",
		"street",
		"town",
		"ghostId",
		"shortName",
		"postCode",
		"cin",
	];

	// Set default query parameters
	const queryParamsRegions = {
		fields: ["name"],
		populate: {
			schools: {
				fields: schoolFields,
				populate: ["schoolTypes"],
			},
			childRegions: {
				// stát
				fields: ["name"],
				populate: {
					schools: {
						fields: schoolFields,
						populate: ["schoolTypes"],
					},
					childRegions: {
						// kraj
						fields: ["name"],
						populate: {
							schools: {
								fields: schoolFields,
								populate: ["schoolTypes"],
							},
							childRegions: {
								// okres
								fields: ["name"],
								populate: {
									schools: {
										fields: schoolFields,
										populate: ["schoolTypes"],
									},
									childRegions: {
										// to be safe
										fields: ["name"],
										populate: {
											schools: {
												fields: schoolFields,
												populate: ["schoolTypes"],
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	};

	// Serialize query parameters
	const queryString = stringify(queryParamsRegions, { encodeValuesOnly: true });
	fullUrlRegions.search = `?${queryString}`;

	try {
		const response = await fetch(fullUrlRegions.href, {
			headers: {
				Authorization: `Bearer ${FISHBUSH_API_KEY}`,
			},
		});

		if (!response.ok) {
			throw createError({
				statusCode: response.status,
				statusMessage: response.statusText,
			});
		}

		const data = await response.json();

		const getSchools = (
			regions: Array<RegionEntity>,
			level = 0,
			isSK = false,
		) => {
			if (regions.length === 0) return [];
			const schools: Array<SchoolEntity> = [];
			for (const region of regions) {
				const newIsSK =
					isSK || region?.attributes?.name === "Slovenská republika";
				let currentSchools = region?.attributes?.schools?.data ?? [];
				if (newIsSK) {
					currentSchools = currentSchools.map((school) => ({
						...school,
						attributes: {
							...school.attributes,
							name: `${school?.attributes?.name} ${school?.attributes?.town} - ${school?.attributes?.street}`,
						},
					}));
				}

				schools.push(...currentSchools);
				if (!region?.attributes?.childRegions?.data) return [];
				schools.push(
					...getSchools(
						region.attributes.childRegions.data,
						level + 1,
						newIsSK,
					),
				);
			}

			return schools;
		};

		cachedSchools = getSchools([data.data]).filter((school) => {
			return school?.attributes?.schoolTypes?.data.some((schoolType) =>
				schoolTypes.includes(schoolType?.id?.toString()),
			);
		});

		return cachedSchools;
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);
		throw createError({
			statusCode: 500,
			statusMessage: `${error}`,
		});
	}
});
