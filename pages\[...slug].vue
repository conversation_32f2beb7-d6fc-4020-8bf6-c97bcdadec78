<!-- /pages/[...slug].vue -->
<script setup lang="ts">
import type { WebPageBlocksDynamicZone } from "@/models/types";
import { useHead } from "@vueuse/head";
import { ReasonPhrases as RP, StatusCodes as SC } from "http-status-codes";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const event = useRequestEvent();
const cmsPage = event?.context?.webPageData;

if (import.meta.server) {
	if (event?.context.error) {
		throw event.context.error;
	}
	if (!cmsPage) {
		throw createError({
			statusCode: SC.NOT_FOUND,
			statusMessage: RP.NOT_FOUND,
		});
	}
}

// Set the page title and omit '|' if there is no title
useHead(() => {
	const title = cmsPage?.title
		? `${cmsPage.title} | Tour de App`
		: t("meta.title");
	const description = cmsPage?.metaDescription ?? t("meta.description");
	const image = t("meta.image");
	return {
		title: title,
		meta: [
			{
				name: "google-site-verification",
				content: "SpVcESVrPL3wu7JhdAh0LfqCrDnXfMHj7N7hpJRTeiQ",
			},
			{ name: "description", content: description },
			{ property: "og:title", content: title },
			{ property: "og:description", content: description },
			{ property: "og:image", content: image },
			{ property: "og:site_name", content: "Tour de App" },
			{ name: "twitter:card", content: "summary_large_image" },
			{ name: "twitter:title", content: title },
			{ name: "twitter:description", content: description },
			{ name: "twitter:image", content: image },
			{ name: "theme-color", content: "#EF8A17" },
		],
	};
});

const getMetaProps = (block: Exclude<WebPageBlocksDynamicZone, Error>) => {
	// In Strapi 5, extract all properties except content and __component as metadata
	const { content, __component, ...metaProps } = block as Record<
		string,
		unknown
	>;
	return metaProps;
};

const componentName = (componentString: string) => {
	const parts = componentString.split(".");
	if (parts.length > 1) {
		const nameParts = parts[1]
			.split("-")
			.map((part) => part.charAt(0).toUpperCase() + part.slice(1))
			.join("");
		return `ContentBlocks${nameParts}`;
	}
	return "";
};
</script>

<template>
	<div v-if="cmsPage" :key="$route.fullPath">
		<div v-for="block in cmsPage?.blocks" :key="block.id">
			<component :is="componentName(block.__component)" :content="block.content ?? ''" :meta="getMetaProps(block)" />
		</div>
	</div>
</template>