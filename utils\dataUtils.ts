import { parseHTML } from "linkedom";

export type ImageBulk = {
	defaultImageUrl: string;
	thumbnailImageUrl: string;
	smallImageUrl: string;
	mediumImageUrl: string;
	largeImageUrl: string;
};

function ensureTrailingSlash(url: string): string {
	return url.replace(/\/?$/, "/");
}

export function getBaseUrl(): string {
	// Get config inside the function instead of at module level
	const config = useRuntimeConfig();
	const FISHBUSH_API_URL = config.public?.FISHBUSH_API_URL;
	return ensureTrailingSlash(FISHBUSH_API_URL);
}

export function createImageUrls(f: UploadFile) {
	function createImageUrl(relativeUrl: string | null) {
		if (typeof relativeUrl !== "string" || !relativeUrl) return null;

		// Ensure baseUrl ends with a slash
		const baseUrl = getBaseUrl();

		// Remove leading slash from the relativeUrl to avoid overwriting the baseUrl path
		const sanitizedRelativeUrl = relativeUrl.replace(/^\//, "");

		return new URL(sanitizedRelativeUrl, baseUrl).href;
	}

	if (!f.url) throw Error("Incomplete image file");
	const defaultImageUrl = createImageUrl(f.url);
	if (!defaultImageUrl) throw Error("Incomplete image file");

	const formats = f.formats;
	const thumbnailImageUrl =
		createImageUrl(formats?.thumbnail?.url ?? null) ?? defaultImageUrl;
	const smallImageUrl =
		createImageUrl(formats?.small?.url ?? null) ?? thumbnailImageUrl;
	const mediumImageUrl =
		createImageUrl(formats?.medium?.url ?? null) ?? smallImageUrl;
	const largeImageUrl =
		createImageUrl(formats?.large?.url ?? null) ?? mediumImageUrl;

	const imageBulk: ImageBulk = {
		defaultImageUrl: defaultImageUrl,
		thumbnailImageUrl: thumbnailImageUrl,
		smallImageUrl: smallImageUrl,
		mediumImageUrl: mediumImageUrl,
		largeImageUrl: largeImageUrl,
	};

	return imageBulk;
}

export function processHtmlContent(
	htmlContent: string,
	locale: string,
): string {
	// Ensure there's at least a root element
	const wrappedHtml = `<html><body>${htmlContent}</body></html>`;

	const { document } = parseHTML(wrappedHtml);

	// Now document.body is guaranteed to exist
	for (const el of document.querySelectorAll("a[href]")) {
		const href = el.getAttribute("href");
		if (href && !/^[a-zA-Z][a-zA-Z\d+\-.]*:/.test(href)) {
			if (locale !== "cs") {
				const localePrefix = `/${locale}`;
				if (!href.startsWith(`${localePrefix}/`) && href !== localePrefix) {
					const newHref = `${localePrefix}${href.startsWith("/") ? "" : "/"}${href}`;
					el.setAttribute("href", newHref);
				}
			}
		}
	}

	for (const el of document.querySelectorAll("img[src], a[href]")) {
		const attr = el.tagName === "IMG" ? "src" : "href";
		const url = el.getAttribute(attr);
		const baseUrl = getBaseUrl();

		if (url?.startsWith(`${baseUrl}uploads/`)) {
			const relativePath = url.replace(baseUrl, "");
			el.setAttribute(attr, `/api${relativePath}`);
		}
	}

	return document.body?.innerHTML || "";
}
