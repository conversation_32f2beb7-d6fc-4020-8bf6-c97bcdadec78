export interface FBQueryParams {
	fields?: string[] | string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	filters?: Record<string, any>;
	populate?: string | string[] | Record<string, FBQueryParams> | boolean;
	sort?: string | string[];
	pagination?: {
		page?: number;
		pageSize?: number;
		start?: number;
		limit?: number;
	};
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	[key: string]: any;
}

export type ContentBlock = {
	__component: string;
	publishDate: string | null;
	unpublishDate: string | null;
	content: string | null;

	// biome-ignore lint/suspicious/noExplicitAny: Allow for other properties
	[key: string]: any;
};

export interface WebConfigSocial {
	id: number;
	title: string;
	href: string;
	iconUrl: string;
}

export interface WebConfigTheme {
	id: number;
	documentId: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string | null;
	name: string;
	csstag: string;
}

export interface WebConfigDomain {
	id: number;
	documentId: string;
	domain: string;
	defaultLocale: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string | null;
}

export interface WebConfigLocale {
	locale: string;
	domain: string;
}

export interface WebConfigResponse {
	id: number;
	documentId: string;
	name: string;
	title: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string;
	infomail: string;
	socials: WebConfigSocial[];
	webTheme: WebConfigTheme;
	webDomain: WebConfigDomain;
	defaultLocale: string;
	allLocales: WebConfigLocale[];
}
