import { getBaseUrl } from "@/utils/dataUtils";
import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from "h3";
const { FISHBUSH_API_KEY } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
	console.log("Web API called");

	const query = getQuery(event);
	const baseUrl = getBaseUrl();
	const fullUrl = new URL("api/web", baseUrl);

	// Pass all query parameters to the external API
	for (const [key, value] of Object.entries(query)) {
		if (Array.isArray(value)) {
			for (const v of value) {
				fullUrl.searchParams.append(key, v);
			}
		} else {
			fullUrl.searchParams.set(key, value as string);
		}
	}

	try {
		const response = await fetch(fullUrl.href, {
			headers: {
				Authorization: `Bearer ${FISHBUSH_API_KEY}`,
			},
		});

		if (!response.ok) {
			throw createError({
				statusCode: response.status,
				statusMessage: response.statusText,
			});
		}

		return await response.json();
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);
		throw createError({
			statusCode: 500,
			statusMessage: `${error}`,
		});
	}
});
