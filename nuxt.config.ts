import { resolve } from "node:path";

const fonts = [
	"https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap",
	"https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900&display=swap",
];

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	devtools: { enabled: true },
	modules: [
		"@nuxtjs/tailwindcss",
		"@pinia/nuxt",
		"nuxt-zod-i18n",
		"@nuxtjs/i18n",
		"@nuxtjs/sitemap",
		"@nuxtjs/robots",
		"nuxt-schema-org",
		"nuxt-gtag",
	],

	routeRules: {
		"/": { prerender: true },
		"/api/*": { cache: { maxAge: 5 } },
	},

	imports: {
		dirs: ["models"],
	},

	alias: {
		"@": resolve(__dirname),
	},

	plugins: ["~/plugins/autoIdDirective.ts", "~/plugins/i18nSetter.ts"],

	gtag: {
		id: "G-555N9FKLDK",
		initMode: "manual",
	},

	runtimeConfig: {
		FISHBUSH_API_KEY: process.env.FISHBUSH_API_KEY,
		KEYCLOAK_API_URL: process.env.KEYCLOAK_API_URL,
		KEYCLOAK_API_TOKEN: process.env.KEYCLOAK_API_TOKEN,
		DISCORD_WEBHOOK_URL: process.env.DISCORD_WEBHOOK_URL,
		DEFAULT_LOCALE: process.env.DEFAULT_LOCALE,
		public: {
			FISHBUSH_API_URL: process.env.FISHBUSH_API_URL,
		},
	},

	css: [
		"~/assets/css/main.css",
		"@fortawesome/fontawesome-svg-core/styles.css",
	],

	app: {
		head: {
			title: "Tour de App",
			link: fonts.map((font) => ({
				rel: "stylesheet",
				href: font,
				as: "style",
				crossorigin: "anonymous",
			})),
		},
	},

	i18n: {
		lazy: true,
		langDir: "locales/",
		locales: [
			{ code: "cs", name: "Czech", file: "cs.json" },
			{ code: "en", name: "English", file: "en.json" },
		],
		defaultLocale: "cs",
		strategy: "no_prefix",
	},

	sitemap: {
		sources: ["/api/__sitemap__/urls"],
		autoI18n: true,
	},

	robots: {
		disallow: [
			"/api/*",
			"/spin",
			"/maintenance",
			"/flag",
			"/uploads/*",
			"/_nuxt/*",
		],
		sitemap: "/sitemap.xml",
		credits: false,
	},

	schemaOrg: {
		identity: {
			type: "Organization",
			name: "Student Cyber Games z.s.",
			url: "https://scg.cz",
			logo: "https://scg.cz/images/SCG_logo_small.png",
			address: {
				streetAddress: "Křenová 89/19",
				addressLocality: "Brno",
				addressRegion: "CZ",
				postalCode: "602 00",
				addressCountry: "Czech Republic",
			},
			description:
				"Zapsaný spolek, parta kamarádů, zkušený tým přibližně 40 studentů z celé České republiky a dokonce i několika statečných ze Slovenska. Baví nás dělat něco navíc, a tak už od roku 2003 organizujeme soutěže pro naše vrstevníky. Začalo to třemi ročníky ve hře Starcraft a následovaly projekty pIšQworky, Prezentiáda a čerstvě i Tour de App.",
		},
	},

	compatibilityDate: "2024-10-06",
});
