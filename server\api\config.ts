import { getBaseUrl } from "@/utils/dataUtils";
import { createError, define<PERSON>vent<PERSON><PERSON><PERSON>, getQuery } from "h3";
const { FISHBUSH_API_KEY } = useRuntimeConfig();

export default defineEventHandler(async (event) => {
	console.log("Config API called");

	const query = getQuery(event);
	const origin = (query.origin as string) || "";
	const locale = (query.locale as string) || "";

	const baseUrl = getBaseUrl();
	const fullUrl = new URL("api/web-config", baseUrl);

	// Add origin as a query parameter if provided
	if (origin) {
		fullUrl.searchParams.set("origin", origin);
	}

	// Only add locale if it was explicitly provided
	if (locale) {
		fullUrl.searchParams.set("locale", locale);
	}

	try {
		const response = await fetch(fullUrl.href, {
			headers: {
				Authorization: `Bearer ${FISHBUSH_API_KEY}`,
			},
		});

		if (!response.ok) {
			throw createError({
				statusCode: response.status,
				statusMessage: response.statusText,
			});
		}

		return await response.json();
	} catch (error) {
		console.error(`[${new Date().toLocaleString()}] ${error}`);
		throw createError({
			statusCode: 500,
			statusMessage: `${error}`,
		});
	}
});
