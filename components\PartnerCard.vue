<script setup lang="ts">
import { createImageUrls } from "@/utils/dataUtils.js";
import { onBeforeUnmount, ref } from "vue";

defineProps({
	partnerDetails: {
		type: Object,
		required: true,
	},
	groupName: {
		type: String,
		required: false,
	},
	importance: {
		type: String,
		required: true,
	},
});

// State to control the overlay visibility and the rectangle animation stages
const showOverlay = ref(false);
const showRectangle = ref(false);
const expandX = ref(false);
const showContent = ref(false);

// Variables to track mouse movement
let isDragging = false;
let startX = 0;
let startY = 0;
const dragThreshold = 5; // Adjust as needed

// Function to handle click and show the overlay, then start rectangle animation
const timeouts: Ref<NodeJS.Timeout[]> = ref([]);

const handleClick = () => {
	document.addEventListener("keydown", handleKeydown);
	document.addEventListener("mousedown", handleDocumentMouseDown);
	showOverlay.value = true;
	timeouts.value[0] = setTimeout(() => {
		showRectangle.value = true;
		timeouts.value[1] = setTimeout(() => {
			expandX.value = true;
			timeouts.value[2] = setTimeout(() => {
				showContent.value = true; // Show content after the rectangle is fully expanded
			}, 300); // Delay before content appears
		}, 300); // Delay before expanding horizontally
	}, 300); // Delay for the overlay animation to finish
};

// Event listener for keydown (Esc or Space)
const handleKeydown = (event: KeyboardEvent) => {
	if (showRectangle.value) {
		if (event.key === "Escape" || event.key === " ") {
			cancelTimeouts();
		}
	}
};

// Mouse event handlers to detect dragging
const handleDocumentMouseDown = (event: MouseEvent) => {
	isDragging = false;
	startX = event.clientX;
	startY = event.clientY;

	document.addEventListener("mousemove", handleDocumentMouseMove);
	document.addEventListener("mouseup", handleDocumentMouseUp);
};

const handleDocumentMouseMove = (event: MouseEvent) => {
	const dx = Math.abs(event.clientX - startX);
	const dy = Math.abs(event.clientY - startY);
	if (dx + dy > dragThreshold) {
		isDragging = true;
	}
};

const handleDocumentMouseUp = (_: MouseEvent) => {
	document.removeEventListener("mousemove", handleDocumentMouseMove);
	document.removeEventListener("mouseup", handleDocumentMouseUp);

	if (showRectangle.value && !isDragging) {
		cancelTimeouts();
	}
};

onBeforeUnmount(() => {
	try {
		document.removeEventListener("keydown", handleKeydown);
		document.removeEventListener("mousedown", handleDocumentMouseDown);
		document.removeEventListener("mousemove", handleDocumentMouseMove);
		document.removeEventListener("mouseup", handleDocumentMouseUp);
		cancelTimeouts();
	} catch (error) {
		console.error("Error removing event listeners", error);
	}
});

// Function to cancel all timeouts
const cancelTimeouts = () => {
	for (const timeout of timeouts.value) {
		clearTimeout(timeout);
	}

	showOverlay.value = false;
	showRectangle.value = false;
	expandX.value = false;
	showContent.value = false;
};
</script>

<template>
  <div :class="importance" class="lg:p-8 p-4">
    <div v-if="groupName" class="mb-4 text-center text-tda-300 text-lg font-bold">
      {{ groupName }}
    </div>
    <div @click="handleClick" class="text-white hover:text-white text-center cursor-pointer">
      <div class="card tda-shadow p-7 aspect-square flex items-center justify-center">
        <span v-if="!partnerDetails.details.logo.data" class="text-3xl">{{ partnerDetails.details.name }}</span>
        <picture v-else>
          <source :srcset='createImageUrls(partnerDetails.details.logo.data as UploadFileEntity).defaultImageUrl'>
          <img class="object-contain w-full h-full"
            :src='createImageUrls(partnerDetails.details.logo.data as UploadFileEntity).defaultImageUrl'
            :alt="partnerDetails.details.name + ' logo'">
        </picture>
      </div>
    </div>

    <!-- Overlay -->
    <div v-if="showOverlay" class="overlay fixed inset-0 z-40 bg-gray-800 bg-opacity-50 backdrop-blur-md transition-opacity duration-300"></div>

    <!-- Rectangle animation -->
    <div v-if="showRectangle"
      :class="{'expand-x': expandX}"
      class="rectangle fixed z-50 bg-background tda-line transition-all duration-300 border-tda-300 border-4">
      
      <!-- Content that fades in -->
      <div v-if="showContent" class="content opacity-0 transition-opacity duration-100 text-white p-8 row h-full">
        <div class="pr-4 w-full md:w-1/2 overflow-y-auto max-h-full scroll-smooth scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar-thin scrollbar-thumb-tda-200 scrollbar-track-[#171b23]">
          <a :href='partnerDetails.details.url'>
            <h2 class="text-2xl mb-4">{{ partnerDetails.details.name }}</h2>
          </a>
          <div class="max-h-full lg:pr-6 text-justify" v-html="partnerDetails.details.text" v-auto-id>

          </div>
        </div>
        <div class="w-full h-full md:w-1/2 hidden md:block">
          <picture>
            <source :srcset='createImageUrls(partnerDetails.details.logo.data as UploadFileEntity).defaultImageUrl'>
            <img class="object-contain w-full h-full pl-4"
              :src='createImageUrls(partnerDetails.details.logo.data as UploadFileEntity).defaultImageUrl'
              :alt="partnerDetails.details.name + ' logo'">
          </picture>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.high {
  @apply w-full sm:w-1/3;
}

.medium {
  @apply w-1/2 sm:w-1/4;
}

.low {
  @apply w-1/3 sm:w-1/5;
}

.overlay {
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.rectangle {
  top: 50%;
  left: 50%;
  width: 4px;
  height: 0;
  transform: translate(-50%, -50%);
  animation: expandY 0.3s forwards;
}

.expand-x {
  @apply w-[90vw] h-[90vh] md:w-[70vw] md:h-[70vh] 
}

.content {
  opacity: 0;
  animation: fadeInContent 0.1s forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes expandY {
  to {
    height: 70vh;
  }
}

@keyframes fadeInContent {
  to {
    opacity: 1;
  }
}
</style>
