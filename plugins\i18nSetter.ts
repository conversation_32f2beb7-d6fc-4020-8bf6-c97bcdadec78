export default defineNuxtPlugin((nuxtApp) => {
	// get the i18n instance
	const i18n = nuxtApp.$i18n;

	// get the locale from the context
	const event = useRequestEvent();
	if (!event) {
		return;
	}

	const locale = event.context.locale;

	// set the locale
	if (locale) {
		// biome-ignore lint/suspicious/noExplicitAny: I cannot be bothered to fix the types for this
		(i18n as any).setLocale(locale);
	}
});
