<script setup lang="ts">
import fishbushDead from "@/public/fishbush_dead.gif";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
	code: {
		type: Number,
		required: true,
	},
});
</script>

<template>
  <div class="content">
    <div class="content-container text-center my-8">
      <h6>[{{props.code}}] {{ t('error_500_msg')}}</h6>
    </div>
  </div>
  <div class="w-full h-full text-center flex items-center justify-center">
    <img :src="fishbushDead" alt="Upsík, něco se nepovedlo" class="fishbush-down-img">
  </div>
</template>

<style scoped>
.fishbush-down-img {
  @apply object-contain m-auto p-4 w-8/12 md:w-6/12 lg:w-4/12 xl:w-3/12;
}
</style>