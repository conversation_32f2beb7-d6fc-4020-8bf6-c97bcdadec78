<!-- error.vue (v kořeni projektu) -->
<template>
  <!-- obal<PERSON>me to do default layoutu -->
  <NuxtLayout name="default">
    <div class="min-h-screen flex flex-col items-center justify-center p-8 text-center">
      <h1 class="text-6xl font-bold mb-4">{{ error?.statusCode }}</h1>
      <p class="text-xl mb-6">
        {{ error?.statusCode === 404 ? 'Stránka nenalezena' : 'Cosi se pokazilo' }}
      </p>
      <p>
        {{ error?.message }}
      </p>
      <NuxtLink to="/" class="px-4 py-2 bg-accent rounded-lg text-white">
        Zpět na úvod
      </NuxtLink>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import { useError } from "#app";
const error = useError();
</script>
