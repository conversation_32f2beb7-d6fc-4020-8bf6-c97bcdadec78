<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    content: {
        type: String,
        required: true,
    },
    meta: {
        type: Object,
        required: false,
    },
});

const currentTime = ref(Date.now());
let interval: NodeJS.Timeout | null = null;

onMounted(() => {
    interval = setInterval(() => {
        currentTime.value = Date.now();
    }, 1000);
});

onUnmounted(() => {
    if (interval) {
        clearInterval(interval);
    }
});

const transformedContent = computed(() => {
    if (!props.meta?.targetTime) return props.content;
    
    const targetTime = new Date(props.meta.targetTime).getTime();
    const timeDiff = Math.max(0, targetTime - currentTime.value);
    
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
    
    const countdownHtml = timeDiff > 0 
        ? `<span class="countdown">${days}d ${hours}h ${minutes}m ${seconds}s</span>`
        : `<span class="countdown expired">Čas vypršel</span>`;
    
    return props.content.replace(/<countdown[^>]*>/g, countdownHtml);
});
</script>

<template>
  <div v-html="transformedContent" v-auto-id></div>
</template>